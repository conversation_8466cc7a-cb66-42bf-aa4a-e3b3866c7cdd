package file.engine.utils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import file.engine.entity.SearchResult;
import file.engine.entity.UwpResult;
import file.engine.utils.file.FileUtil;

public class RankUtil {

    private static final Cache<String, Integer> rankCache = Caffeine.newBuilder()
            .softValues()
            .maximumSize(1000)
            .build();


    /**
     * 计算SearchResult的排序分数
     *
     * @param searchResult 搜索结果
     * @param keywords     搜索关键词
     * @return 排序分数（越小越靠前）
     */
    public static double calculateSearchResultRank(SearchResult searchResult, String[] keywords) {
        // 缓存key
        String cacheKey = searchResult.getPath() + ":" + String.join(",", keywords);
        Integer cachedRank = rankCache.getIfPresent(cacheKey);
        if (cachedRank != null) {
            return cachedRank;
        }
        String fileName = FileUtil.getFileName(searchResult.getPath());
        String word = FileUtil.removeFileNameSuffix(fileName);

        double rank = 0;

        // 优先级结果权重 (最重要)
        if (searchResult.isPrioritizeResult()) {
            rank -= 1000;
        }

        // 模糊匹配权重
        if (searchResult.isFuzzyMatched()) {
            rank += 100;
        }

        // 文件名长度权重
        rank += fileName.length() * 50;

        // 连续匹配权重（考虑连续性和位置）
        double consecutiveMatchScore = getConsecutiveMatchScore(fileName, keywords);
        rank -= consecutiveMatchScore * 30;

        // 有意义概率权重（按单词分别计算）
        double meaningfulProbability = calculateMeaningfulProbability(word);
        rank -= meaningfulProbability * 100;

        // 添加路径哈希作为次要排序条件，确保唯一性
        var pathHashCode = Math.abs(searchResult.getPath().hashCode());
        rank += pathHashCode * 0.000001;

        // 缓存结果
        rankCache.put(cacheKey, (int) rank);

        return rank;
    }

    /**
     * 计算UwpResult的排序分数
     *
     * @param uwpResult UWP搜索结果
     * @param keywords  搜索关键词
     * @return 排序分数（越小越靠前）
     */
    public static double calculateUwpResultRank(UwpResult uwpResult, String[] keywords) {
        String displayName = uwpResult.getDisplayName();

        // 缓存key
        String cacheKey = "uwp:" + displayName + ":" + String.join(",", keywords);
        Integer cachedRank = rankCache.getIfPresent(cacheKey);
        if (cachedRank != null) {
            return cachedRank;
        }

        double rank = 0;

        // 显示名称长度权重
        rank += displayName.length() * 50;

        // 连续匹配权重（考虑连续性和位置）
        double consecutiveMatchScore = getConsecutiveMatchScore(displayName, keywords);
        rank -= consecutiveMatchScore * 30;

        // 有意义概率权重（按单词分别计算）
        double meaningfulProbability = calculateMeaningfulProbability(displayName);
        rank -= meaningfulProbability * 100;

        // 添加显示名称哈希作为次要排序条件，确保唯一性
        rank += Math.abs(displayName.hashCode()) * 0.000001;

        // 缓存结果
        rankCache.put(cacheKey, (int) rank);

        return rank;
    }

    /**
     * 获取连续匹配权重分数（支持多个关键词）
     *
     * @param fileName 文件名
     * @param keywords 关键词数组
     * @return 连续匹配权重分数（越高越好）
     */
    public static double getConsecutiveMatchScore(String fileName, String[] keywords) {
        if (fileName == null || fileName.isEmpty() || keywords == null || keywords.length == 0) {
            return 0;
        }
        double totalScore = 0;
        for (String keyword : keywords) {
            if (keyword.charAt(0) == '?') {
                keyword = keyword.substring(1);
            }
            totalScore += getConsecutiveMatchScore(fileName, keyword);
        }
        return totalScore;
    }

    /**
     * 获取连续匹配的权重分数（考虑连续性和位置）
     *
     * @param fileName 文件名
     * @param keyword  关键词
     * @return 连续匹配权重分数（越高越好）
     */
    private static double getConsecutiveMatchScore(String fileName, String keyword) {
        if (fileName == null || fileName.isEmpty() || keyword == null || keyword.isEmpty()) {
            return 0;
        }

        // 转换为小写进行匹配
        String lowerFileName = fileName.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();

        double bestScore = 0;

        // 尝试从每个位置开始匹配
        for (int startPos = 0; startPos < lowerFileName.length(); startPos++) {
            int keywordIndex = 0;
            int totalMatches = 0;
            int currentConsecutive = 0;
            int maxConsecutive = 0;
            int lastMatchPos = -1;
            int gaps = 0; // 记录间断次数

            // 从当前位置开始匹配关键词
            for (int i = startPos; i < lowerFileName.length() && keywordIndex < lowerKeyword.length(); i++) {
                if (lowerFileName.charAt(i) == lowerKeyword.charAt(keywordIndex)) {
                    totalMatches++;
                    keywordIndex++;

                    // 检查连续性
                    if (lastMatchPos == -1 || i == lastMatchPos + 1) {
                        currentConsecutive++;
                        maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
                    } else {
                        currentConsecutive = 1;
                        gaps++; // 记录间断
                    }
                    lastMatchPos = i;
                }
            }

            // 如果完全匹配了关键词
            if (keywordIndex == lowerKeyword.length()) {
                // 计算分数：匹配长度 * 位置权重 * 连续性奖励
                double positionWeight = 1.0 - (double) startPos / lowerFileName.length(); // 位置越靠前权重越高
                double continuityRatio = (double) maxConsecutive / totalMatches; // 连续性比例
                double gapPenalty = 1.0 - (double) gaps / totalMatches; // 间断惩罚
                double continuityBonus = 1.0 + continuityRatio * 2.0 + gapPenalty; // 连续性奖励
                double currentScore = totalMatches * positionWeight * continuityBonus;
                bestScore = Math.max(bestScore, currentScore);
            }
        }

        return bestScore;
    }

    /**
     * 测试方法：验证连续匹配分数计算
     */
    public static void testConsecutiveMatchScore() {
        String keyword = "geekexe";
        String fileName1 = "geek.exe";
        String fileName2 = "geek64.exe";

        double score1 = getConsecutiveMatchScore(fileName1, keyword);
        double score2 = getConsecutiveMatchScore(fileName2, keyword);

        System.out.println("Keyword: " + keyword);
        System.out.println("FileName: " + fileName1 + " -> Score: " + score1);
        System.out.println("FileName: " + fileName2 + " -> Score: " + score2);
        System.out.println("Difference: " + Math.abs(score1 - score2));
    }

    /**
     * 计算有意义概率（按单词分别计算）
     *
     * @param text 要分析的文本
     * @return 平均有意义概率
     */
    private static double calculateMeaningfulProbability(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // 按空格分割单词
        var pattern = RegexUtil.getPattern("\\s+", 0);
        String[] words = pattern.split(text);
        if (words.length == 0) {
            return 0;
        }

        double totalProbability = 0;
        int validWordCount = 0;

        for (String word : words) {
            // 过滤掉空字符串和只包含特殊字符的单词
            if (word != null && !word.isEmpty()) {
                totalProbability += GibberishDetectorUtil.getMeaningfulProbability(word);
                validWordCount++;
            }
        }

        // 返回平均概率，如果没有有效单词则返回0
        return validWordCount > 0 ? totalProbability / validWordCount : 0;
    }
}
