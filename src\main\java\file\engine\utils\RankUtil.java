package file.engine.utils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import file.engine.entity.SearchResult;
import file.engine.entity.UwpResult;
import file.engine.utils.file.FileUtil;

public class RankUtil {

    private static final Cache<String, Integer> rankCache = Caffeine.newBuilder()
            .softValues()
            .maximumSize(1000)
            .build();


    /**
     * 计算SearchResult的排序分数
     *
     * @param searchResult 搜索结果
     * @param keywords     搜索关键词
     * @return 排序分数（越小越靠前）
     */
    public static double calculateSearchResultRank(SearchResult searchResult, String[] keywords) {
        // 缓存key
        String cacheKey = searchResult.getPath() + ":" + String.join(",", keywords);
        Integer cachedRank = rankCache.getIfPresent(cacheKey);
        if (cachedRank != null) {
            return cachedRank;
        }
        String fileName = FileUtil.getFileName(searchResult.getPath());
        String word = FileUtil.removeFileNameSuffix(fileName);

        double rank = 0;

        // 优先级结果权重 (最重要)
        if (searchResult.isPrioritizeResult()) {
            rank -= 1000;
        }

        // 模糊匹配权重
        if (searchResult.isFuzzyMatched()) {
            rank += 100;
        }

        // 文件名长度权重
        rank += fileName.length() * 50;

        // 连续匹配权重（考虑连续性和位置）
        double consecutiveMatchScore = getConsecutiveMatchScore(fileName, keywords);
        rank -= consecutiveMatchScore * 30;

        // 有意义概率权重（按单词分别计算）
        double meaningfulProbability = calculateMeaningfulProbability(word);
        rank -= meaningfulProbability * 100;

        // 添加路径哈希作为次要排序条件，确保唯一性
        var pathHashCode = Math.abs(searchResult.getPath().hashCode());
        rank += pathHashCode * 0.000001;

        // 缓存结果
        rankCache.put(cacheKey, (int) rank);

        return rank;
    }

    /**
     * 计算UwpResult的排序分数
     *
     * @param uwpResult UWP搜索结果
     * @param keywords  搜索关键词
     * @return 排序分数（越小越靠前）
     */
    public static double calculateUwpResultRank(UwpResult uwpResult, String[] keywords) {
        String displayName = uwpResult.getDisplayName();

        // 缓存key
        String cacheKey = "uwp:" + displayName + ":" + String.join(",", keywords);
        Integer cachedRank = rankCache.getIfPresent(cacheKey);
        if (cachedRank != null) {
            return cachedRank;
        }

        double rank = 0;

        // 显示名称长度权重
        rank += displayName.length() * 50;

        // 连续匹配权重（考虑连续性和位置）
        double consecutiveMatchScore = getConsecutiveMatchScore(displayName, keywords);
        rank -= consecutiveMatchScore * 30;

        // 有意义概率权重（按单词分别计算）
        double meaningfulProbability = calculateMeaningfulProbability(displayName);
        rank -= meaningfulProbability * 100;

        // 添加显示名称哈希作为次要排序条件，确保唯一性
        rank += Math.abs(displayName.hashCode()) * 0.000001;

        // 缓存结果
        rankCache.put(cacheKey, (int) rank);

        return rank;
    }

    /**
     * 获取连续匹配权重分数（支持多个关键词）
     *
     * @param fileName 文件名
     * @param keywords 关键词数组
     * @return 连续匹配权重分数（越高越好）
     */
    public static double getConsecutiveMatchScore(String fileName, String[] keywords) {
        if (fileName == null || fileName.isEmpty() || keywords == null || keywords.length == 0) {
            return 0;
        }
        double totalScore = 0;
        for (String keyword : keywords) {
            if (keyword.charAt(0) == '?') {
                keyword = keyword.substring(1);
            }
            totalScore += getConsecutiveMatchScore(fileName, keyword);
        }
        return totalScore;
    }

    /**
     * 获取连续匹配的权重分数（考虑连续性和位置）
     *
     * @param fileName 文件名
     * @param keyword  关键词
     * @return 连续匹配权重分数（越高越好）
     */
    private static double getConsecutiveMatchScore(String fileName, String keyword) {
        if (fileName == null || fileName.isEmpty() || keyword == null || keyword.isEmpty()) {
            return 0;
        }

        // 转换为小写进行匹配
        String lowerFileName = fileName.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();

        double bestScore = 0;

        // 尝试从每个位置开始匹配
        for (int startPos = 0; startPos < lowerFileName.length(); startPos++) {
            MatchResult matchResult = findMatchFromPosition(lowerFileName, lowerKeyword, startPos);

            // 如果完全匹配了关键词，计算分数
            if (matchResult.completeMatch()) {
                double currentScore = calculateMatchScore(matchResult, startPos, lowerFileName.length());
                bestScore = Math.max(bestScore, currentScore);
            }
        }

        return bestScore;
    }

    /**
     * 从指定位置开始查找匹配
     */
    private static MatchResult findMatchFromPosition(String fileName, String keyword, int startPos) {
        int keywordIndex = 0;
        int totalMatches = 0;
        int currentConsecutive = 0;
        int maxConsecutive = 0;
        int lastMatchPos = -1;
        int consecutiveSegments = 0;
        boolean inConsecutiveSegment = false;

        // 从当前位置开始匹配关键词
        for (int i = startPos; i < fileName.length() && keywordIndex < keyword.length(); i++) {
            if (fileName.charAt(i) == keyword.charAt(keywordIndex)) {
                totalMatches++;
                keywordIndex++;

                // 检查是否连续匹配
                if (lastMatchPos == -1 || i == lastMatchPos + 1) {
                    // 连续匹配
                    currentConsecutive++;
                    maxConsecutive = Math.max(maxConsecutive, currentConsecutive);

                    // 如果刚开始一个新的连续段
                    if (!inConsecutiveSegment) {
                        consecutiveSegments++;
                        inConsecutiveSegment = true;
                    }
                } else {
                    // 非连续匹配，开始新的连续段
                    currentConsecutive = 1;
                    consecutiveSegments++;
                }
                lastMatchPos = i;
            }
        }

        return new MatchResult(keywordIndex == keyword.length(), totalMatches, maxConsecutive, consecutiveSegments);
    }

    /**
     * 计算匹配分数
     */
    private static double calculateMatchScore(MatchResult matchResult, int startPos, int fileNameLength) {
        if (matchResult.totalMatches == 0) {
            return 0;
        }

        // 位置权重：越靠前权重越高
        double positionWeight = 1.0 - (double) startPos / fileNameLength;

        // 连续性比例：最长连续段占总匹配的比例
        double continuityRatio = (double) matchResult.maxConsecutive / matchResult.totalMatches;

        // 段数惩罚：连续段越多，惩罚越大
        double segmentPenalty = Math.max(0.1, 1.0 / Math.max(1, matchResult.consecutiveSegments));

        // 连续性奖励
        double continuityBonus = 1.0 + continuityRatio * 2.0;
        double finalBonus = continuityBonus * segmentPenalty;

        return matchResult.totalMatches * positionWeight * finalBonus;
    }

    /**
     * 匹配结果数据类
     */
    private record MatchResult(boolean completeMatch, int totalMatches, int maxConsecutive, int consecutiveSegments) {
    }

    /**
     * 计算有意义概率（按单词分别计算）
     *
     * @param text 要分析的文本
     * @return 平均有意义概率
     */
    private static double calculateMeaningfulProbability(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // 按空格分割单词
        var pattern = RegexUtil.getPattern("\\s+", 0);
        String[] words = pattern.split(text);
        if (words.length == 0) {
            return 0;
        }

        double totalProbability = 0;
        int validWordCount = 0;

        for (String word : words) {
            // 过滤掉空字符串和只包含特殊字符的单词
            if (word != null && !word.isEmpty()) {
                totalProbability += GibberishDetectorUtil.getMeaningfulProbability(word);
                validWordCount++;
            }
        }

        // 返回平均概率，如果没有有效单词则返回0
        return validWordCount > 0 ? totalProbability / validWordCount : 0;
    }
}
