package file.engine.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class RankUtilTest {

    @Test
    public void testConsecutiveMatchScore_GeekExeExample() {
        String[] keywords = {"geekexe"};
        
        // 测试 geek.exe - 更连续的匹配
        double score1 = RankUtil.getConsecutiveMatchScore("geek.exe", keywords);
        
        // 测试 geek64.exe - 较分散的匹配
        double score2 = RankUtil.getConsecutiveMatchScore("geek64.exe", keywords);
        
        System.out.println("geek.exe score: " + score1);
        System.out.println("geek64.exe score: " + score2);
        
        // geek.exe 应该得到更高的分数，因为匹配更连续
        assertTrue(score1 > score2, 
            String.format("geek.exe (%.3f) should have higher score than geek64.exe (%.3f)", 
                         score1, score2));
    }
    
    @Test
    public void testConsecutiveMatchScore_DensityComparison() {
        String[] keywords = {"abc"};
        
        // 完全连续匹配
        double score1 = RankUtil.getConsecutiveMatchScore("abc.txt", keywords);
        
        // 分散匹配
        double score2 = RankUtil.getConsecutiveMatchScore("axbxc.txt", keywords);
        
        System.out.println("abc.txt score: " + score1);
        System.out.println("axbxc.txt score: " + score2);
        
        // 连续匹配应该得到更高分数
        assertTrue(score1 > score2, 
            String.format("abc.txt (%.3f) should have higher score than axbxc.txt (%.3f)", 
                         score1, score2));
    }
}
